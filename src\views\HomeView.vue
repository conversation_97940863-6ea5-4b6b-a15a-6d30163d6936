<script setup>
import { ref, onMounted, onBeforeUnmount, watch } from 'vue';
import { games, hotGames, bestGames, categories } from '../data/games.js';
import { useRouter } from 'vue-router';
// 导入Swiper相关组件
import { Swiper, SwiperSlide } from 'swiper/vue';
import { Navigation, Pagination, Autoplay, EffectCoverflow } from 'swiper/modules';
// 导入Swiper样式
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import 'swiper/css/effect-coverflow';
// 导入分类列表组件
import CategoryList from '../components/CategoryList.vue';

const router = useRouter();
const carouselGames = ref([
  {"id": "tap-to-fish",
    "title": "Tap to Fish",
    "icon_url": "https://img.gamemonetize.com/5duk2ba6p6zl64noopfrpv3fv214w1r1/512x384.jpg",
    "description": "Cast your line, reel in the rewards, and grow your fishing empire! In Tap to Fish, you begin with simple gear and work your way up by catching and selling fish for gold. Every catch earns you coins to upgrade your fishing rod \n boosting its capacity and reaching deeper waters. With its smooth gameplay and vibrant visuals, this hypercasual adventure is easy to pick up and hard to put down."
  },
  {
    "id": "cake-sorting-deluxe",
    "title": "Cake Sorting Deluxe",
    "icon_url": "https://img.gamemonetize.com/dc07yhnrg9jm7eoy2d8gg37r6rc2iy3w/512x384.jpg",
    "description": "Cake Sorting Deluxe is a fun and relaxing puzzle game that challenges your sorting skills! Shift cakes across shelves to make matches of three or more. Clear the board, uncover hidden and shadowed cakes, and solve tricky puzzles as you go. With cute visuals, smooth gameplay, and lots of sweet challenges, Cake Sorting Deluxe is a treat for anyone who loves a good puzzle. Ready to mix, match, and clear the shelves?"
  },
  {
    "id": "escape-from-the-toys-factory",
    "title": "Escape From The Toys Factory",
    "icon_url": "https://img.gamemonetize.com/d8vjyjtwgj1c7opsa1e99ifmvnfvgvn8/512x384.jpg",
    "description": "Complete all Poppy pop it toys to Escape From The Toys Factory!    \n    \nTry to do that as faster as you can!    \n  \nIf you ever know about Poppy scary character, it's Playtime for you to get start in this game.! enjoy it!"
  },
  {
    id: "plants-vs-zombies",
    title: "Plants vs Zombies",
    description: "Play the hit action-strategy adventure where you meet, greet, and defeat legions of hilarious zombies from the dawn of time, to the end of days.",
    icon_url: "https://img.gamemonetize.com/9pxdey5sz516st92w3xdmhw1or74vq14/512x384.jpg"
  },
  {
    id: "stealthy-heist",
    title: "Stealthy Heist",
    description: "Stealthy Heist is a thrilling stealth-action game where you play as a master thief, planning and executing high-stakes heists without leaving a trace.",
    icon_url: "https://img.gamemonetize.com/z135u2wpcnbuxz820lfv631trrkcrhz1/512x384.jpg"
  }
]);

// 暗色模式状态
const isDarkMode = ref(document.documentElement.classList.contains('dark'));

// 切换暗色模式
const toggleDarkMode = () => {
  isDarkMode.value = !isDarkMode.value;
  
  if (isDarkMode.value) {
    document.documentElement.classList.add('dark');
    localStorage.setItem('theme', 'dark');
  } else {
    document.documentElement.classList.remove('dark');
    localStorage.setItem('theme', 'light');
  }
};

// Swiper配置
const swiperOptions = {
  modules: [Navigation, Pagination, Autoplay, EffectCoverflow],
  slidesPerView: 1.5,
  centeredSlides: true,
  spaceBetween: 30,
  loop: true,
  autoplay: {
    delay: 5000,
    disableOnInteraction: false
  },
  pagination: {
    clickable: true,
    dynamicBullets: true
  },
  navigation: true,
  effect: 'coverflow',
  coverflowEffect: {
    rotate: 0,
    stretch: 0,
    depth: 100,
    modifier: 1,
    slideShadows: true
  },
  breakpoints: {
    640: {
      slidesPerView: 1.8
    },
    768: {
      slidesPerView: 2.2
    },
    1024: {
      slidesPerView: 2.5
    }
  }
};

// 添加这些游戏到全局游戏列表，确保详情页能找到
const addCustomGamesToGlobalList = () => {
  // 创建一个Set来存储已有的游戏ID，避免重复添加
  const existingGameIds = new Set(games.map(g => g.id));
  
  // 添加轮播图游戏
  carouselGames.value.forEach(game => {
    if (!existingGameIds.has(game.id)) {
      games.push({...game});
      existingGameIds.add(game.id);
    }
  });
};

onMounted(() => {
  // 添加自定义游戏到全局列表
  addCustomGamesToGlobalList();
  
  // 初始化暗色模式状态
  isDarkMode.value = document.documentElement.classList.contains('dark');
  
  // 监听系统主题变化
  const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
  const handleChange = (e) => {
    if (!localStorage.getItem('theme')) {
      isDarkMode.value = e.matches;
      if (isDarkMode.value) {
        document.documentElement.classList.add('dark');
      } else {
        document.documentElement.classList.remove('dark');
      }
    }
  };
  
  mediaQuery.addEventListener('change', handleChange);
  
  onBeforeUnmount(() => {
    mediaQuery.removeEventListener('change', handleChange);
  });
});

const navigateToGameDetail = (gameId) => {
  router.push(`/detail/${gameId}`);
};
</script>

<template>
  <main class="bg-white dark:bg-gray-900 min-h-screen transition-colors duration-300">
    <!-- 顶部导航栏 -->
    <header class="bg-purple-600 dark:bg-purple-800 py-4 flex justify-between items-center px-4 relative">
      <h1 class="text-3xl font-bold text-white text-center flex-grow">ZAPPLAYFUN.GAMES</h1>
      <div class="flex items-center">
        <!-- 主题切换按钮 -->
        <button 
          @click="toggleDarkMode" 
          class="text-white p-2 rounded-lg hover:bg-purple-700 dark:hover:bg-purple-900 focus:outline-none focus:ring-2 focus:ring-purple-400"
          aria-label="Toggle dark mode"
        >
          <!-- 月亮图标 (暗色模式) -->
          <svg 
            xmlns="http://www.w3.org/2000/svg" 
            class="h-6 w-6" 
            :class="{'hidden': isDarkMode}"
            fill="none" 
            viewBox="0 0 24 24" 
            stroke="currentColor"
          >
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
          </svg>
          <!-- 太阳图标 (亮色模式) -->
          <svg 
            xmlns="http://www.w3.org/2000/svg" 
            class="h-6 w-6" 
            :class="{'hidden': !isDarkMode}"
            fill="none" 
            viewBox="0 0 24 24" 
            stroke="currentColor"
          >
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
          </svg>
        </button>
      </div>
    </header>
    
    <div class="container mx-auto px-4 py-6">
      <!-- 现代化轮播图区域 -->
      <div class="modern-carousel-container py-8 px-6 mb-12">
        <swiper
          :slides-per-view="swiperOptions.slidesPerView"
          :centered-slides="swiperOptions.centeredSlides"
          :space-between="swiperOptions.spaceBetween"
          :loop="swiperOptions.loop"
          :autoplay="swiperOptions.autoplay"
          :pagination="swiperOptions.pagination"
          :navigation="swiperOptions.navigation"
          :effect="swiperOptions.effect"
          :coverflow-effect="swiperOptions.coverflowEffect"
          :breakpoints="swiperOptions.breakpoints"
          :modules="swiperOptions.modules"
          class="mySwiper"
        >
          <swiper-slide 
            v-for="game in carouselGames" 
            :key="game.id" 
            class="carousel-slide"
            @click="navigateToGameDetail(game.id)"
          >
            <div class="slide-content">
              <img :src="game.icon_url" :alt="game.title" class="slide-image" />
              <div class="slide-info">
                <h3 class="slide-title">{{ game.title }}</h3>
                <p class="slide-description">{{ game.description }}</p>
              </div>
            </div>
          </swiper-slide>
        </swiper>
      </div>
      
      <!-- 主要内容区 -->
      <div class="flex flex-col lg:flex-row gap-6">
        <!-- 左侧内容区 - 游戏列表 -->
        <div class="w-full lg:w-3/4">
          <!-- 热门游戏区域 -->
          <section class="py-8">
            <h2 class="text-4xl font-bold text-gray-900 dark:text-white mb-6">Hot Games</h2>
            <div class="border-t border-gray-200 dark:border-gray-700 pt-6">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-6">
                <a 
                  v-for="(game, index) in hotGames.slice(0, 10)" 
                  :key="`hot-${game.id}`" 
                  class="flex items-start gap-4 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 p-2 rounded-lg transition-colors"
                  @click.prevent="navigateToGameDetail(game.id)"
                >
                  <img 
                    :src="game.icon_url" 
                    :alt="game.title" 
                    class="w-24 h-24 object-cover rounded-lg flex-shrink-0"
                  />
                  <div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-1">{{ game.title }}</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400 line-clamp-3">{{ game.description }}</p>
                  </div>
                </a>
              </div>
            </div>
          </section>
          
          <!-- Best Games区域 -->
          <section class="py-8">
            <h2 class="text-4xl font-bold text-gray-900 dark:text-white mb-6">Best Games</h2>
            <div class="border-t border-gray-200 dark:border-gray-700 pt-6">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-6">
                <a 
                  v-for="(game, index) in bestGames.slice(0, 10)" 
                  :key="`best-${game.id}`" 
                  class="flex items-start gap-4 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 p-2 rounded-lg transition-colors"
                  @click.prevent="navigateToGameDetail(game.id)"
                >
                  <img 
                    :src="game.icon_url" 
                    :alt="game.title" 
                    class="w-24 h-24 object-cover rounded-lg flex-shrink-0"
                  />
                  <div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-1">{{ game.title }}</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400 line-clamp-3">{{ game.description }}</p>
                  </div>
                </a>
              </div>
            </div>
          </section>
        </div>
        
        <!-- 右侧分类区 -->
        <div class="w-full lg:w-1/4 pt-8">
          <CategoryList />
        </div>
      </div>
      
      <!-- 介绍性内容区域 -->
      <div class="mt-16">
        <!-- 欢迎介绍 -->
        <div class="mb-8">
          <h2 class="text-sm font-bold text-gray-900 dark:text-white mb-3">Welcome to <a href="https://zapplayfun.games" class="text-blue-600 dark:text-blue-400 hover:underline">https://zapplayfun.games</a> : Free Game Center is an aggregation platform that provides a variety of online games that players can play for free. It usually includes the following features:</h2>
          <ol class="list-decimal pl-5 text-[10px] text-gray-600 dark:text-gray-300 space-y-2 leading-relaxed">
            <li>Diversified games: covering multiple types such as role-playing, casual puzzles, and competitions to meet the interests of different players.</li>
            <li>No need to download: Most games can be played directly in the browser, which is convenient and fast and saves storage space.</li>
            <li>Social functions: Many platforms provide social interaction functions, players can play games with friends or communicate in the community.</li>
            <li>Frequent updates: New games are updated regularly to keep the platform fresh and active.</li>
          </ol>
          <p class="text-[10px] text-gray-600 dark:text-gray-300 mt-2">Free Game Center provides players with a relaxing and enjoyable entertainment environment suitable for leisure time.</p>
        </div>
        
        <!-- 游戏分类介绍 -->
        <div class="mb-8">
          <h2 class="text-sm font-bold text-gray-900 dark:text-white mb-3">Discover Some Exciting Game Categories on <a href="https://zapplayfun.games" class="text-blue-600 dark:text-blue-400 hover:underline">https://zapplayfun.games</a></h2>
          <p class="text-[10px] text-gray-600 dark:text-gray-300 mb-3">Let's dive into the exciting world of game categories that <a href="https://zapplayfun.games" class="text-blue-600 dark:text-blue-400 hover:underline">https://zapplayfun.games</a> offers. Here's a quick rundown of the various game categories:</p>
          <ol class="list-decimal pl-5 text-[10px] text-gray-600 dark:text-gray-300 space-y-2 leading-relaxed">
            <li>
              <strong>Racing</strong><br>
              Revolving around racing, players can drive various types of vehicles to compete on different tracks, pursuing speed and skills.
            </li>
            <li>
              <strong>Casual</strong><br>
              Simple and easy to play games suitable for relaxation and entertainment, usually without complex rules, suitable for players of all ages.
            </li>
            <li>
              <strong>Skill</strong><br>
              Focus on the player's skills and reactions, usually including challenging and difficult operations, testing the player's skills.
            </li>
            <li>
              <strong>Animals</strong><br>
              Games with animal themes may include elements such as cultivation, adventure or simulation, suitable for players who love animals.
            </li>
            <li>
              <strong>Girls</strong><br>
              Games designed specifically for female players, usually involving elements such as fashion, makeup, cultivation, emphasizing creativity and personality.
            </li>
          </ol>
          <p class="text-[10px] text-gray-600 dark:text-gray-300 mt-2">These categories cover a wide range of gaming experiences, catering to different players' needs and preferences.</p>
        </div>
        
        <!-- 如何开始游戏 -->
        <div class="mb-8">
          <h2 class="text-sm font-bold text-gray-900 dark:text-white mb-3">How to play online games at <a href="https://zapplayfun.games" class="text-blue-600 dark:text-blue-400 hover:underline">https://zapplayfun.games</a></h2>
          <p class="text-[10px] text-gray-600 dark:text-gray-300 mb-3">Alright, so you're ready to dive into the world of <a href="https://zapplayfun.games" class="text-blue-600 dark:text-blue-400 hover:underline">https://zapplayfun.games</a> – awesome choice! Here's how to get started:</p>
          <ol class="list-decimal pl-5 text-[10px] text-gray-600 dark:text-gray-300 space-y-2 leading-relaxed">
            <li><strong>Step 1:</strong> Fire up your web browser and head over to <a href="https://zapplayfun.games" class="text-blue-600 dark:text-blue-400 hover:underline">https://zapplayfun.games</a>.</li>
            <li><strong>Step 2:</strong> Take a stroll through our game library and pick out something that catches your eye.</li>
            <li><strong>Step 3:</strong> Click on the game you want to play – it's that simple!</li>
            <li><strong>Step 4:</strong> Get ready to have a blast. Whether you're flying solo or teaming up with friends, the fun never stops at <a href="https://zapplayfun.games" class="text-blue-600 dark:text-blue-400 hover:underline">https://zapplayfun.games</a>.</li>
          </ol>
        </div>
      </div>
      
      <!-- 版权信息 -->
      <div class="mt-12 pt-8 border-t border-gray-200 dark:border-gray-700 text-center text-xs text-gray-500 dark:text-gray-400">
        <p>&copy; {{ new Date().getFullYear() }} ZapPlayFun. All rights reserved.</p>
      </div>
    </div>
  </main>
</template>

<style scoped>
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 现代化轮播图样式 */
.modern-carousel-container {
  position: relative;
  width: 100%;
  overflow: hidden;
}

.carousel-slide {
  transition: all 0.3s ease;
  cursor: pointer;
}

.swiper-slide-active {
  transform: scale(1.05);
  z-index: 2;
}

.swiper-slide-prev,
.swiper-slide-next {
  opacity: 0.8;
}

.slide-content {
  position: relative;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.slide-image {
  width: 100%;
  height: 280px;
  object-fit: cover;
  border-radius: 1rem;
}

.slide-info {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 1.5rem;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0));
  border-bottom-left-radius: 1rem;
  border-bottom-right-radius: 1rem;
  color: white;
}

.slide-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.slide-description {
  font-size: 0.875rem;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 自定义Swiper导航按钮 */
:deep(.swiper-button-next),
:deep(.swiper-button-prev) {
  background-color: rgba(0, 0, 0, 0.3);
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  transition: background-color 0.3s;
}

:deep(.swiper-button-next:hover),
:deep(.swiper-button-prev:hover) {
  background-color: rgba(0, 0, 0, 0.5);
}

:deep(.swiper-button-next:after),
:deep(.swiper-button-prev:after) {
  font-size: 18px;
}

/* 自定义Swiper分页指示器 */
:deep(.swiper-pagination) {
  bottom: -30px;
}

:deep(.swiper-pagination-bullet) {
  width: 8px;
  height: 8px;
  background-color: #D1D5DB;
  opacity: 0.7;
  transition: all 0.3s;
}

:deep(.swiper-pagination-bullet-active) {
  width: 24px;
  border-radius: 4px;
  background-color: #8B5CF6;
  opacity: 1;
}

/* 暗色模式下的Swiper样式调整 */
.dark :deep(.swiper-pagination-bullet) {
  background-color: #4B5563;
}

.dark :deep(.swiper-pagination-bullet-active) {
  background-color: #8B5CF6;
}

.dark .slide-content {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
}
</style> 