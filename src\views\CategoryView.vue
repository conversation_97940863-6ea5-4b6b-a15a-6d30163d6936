<script setup>
import { ref, onMounted, computed, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { games } from '../data/games.js';
import CategoryList from '../components/CategoryList.vue';

const route = useRoute();
const router = useRouter();

// 获取当前分类名称
const categoryName = computed(() => route.params.name);

// 获取该分类下的所有游戏
const categoryGames = computed(() => {
  // 如果分类是"Other"，则需要特殊处理
  if (categoryName.value === 'Other') {
    // 获取所有主要分类（除Other外的前5个分类）
    const mainCategories = getMainCategories();
    
    // 返回不属于主要分类的游戏
    return games.filter(game => {
      if (!game.categories || !Array.isArray(game.categories)) return true;
      return !game.categories.some(cat => mainCategories.includes(cat));
    });
  }
  
  // 返回属于当前分类的游戏
  return games.filter(game => {
    if (!game.categories || !Array.isArray(game.categories)) return false;
    return game.categories.includes(categoryName.value);
  });
});

// 获取主要分类（除Other外的前5个分类）
const getMainCategories = () => {
  const categoryCounts = {};
  
  // 统计每个分类的游戏数量
  games.forEach(game => {
    if (game.categories && Array.isArray(game.categories)) {
      game.categories.forEach(category => {
        if (!categoryCounts[category]) {
          categoryCounts[category] = 0;
        }
        categoryCounts[category]++;
      });
    }
  });
  
  // 转换为数组并排序
  const sortedCategories = Object.keys(categoryCounts).map(name => ({
    name,
    count: categoryCounts[name]
  })).sort((a, b) => b.count - a.count);
  
  // 获取前5个分类的名称
  return sortedCategories.slice(0, 5).map(cat => cat.name);
};

// 导航到游戏详情页
const navigateToGameDetail = (gameId) => {
  router.push(`/detail/${gameId}`);
};

// 返回上一页
const goBack = () => {
  router.go(-1);
};

// 返回首页
const goHome = () => {
  router.push('/');
};

// 获取分类图标
const getCategoryIcon = (categoryName) => {
  const categoryIcons = {
    'Racing': '🏎️',
    'Action': '💥',
    'Adventure': '🗺️',
    'Arcade': '🕹️',
    'Puzzle': '🧩',
    'Shooting': '🔫',
    'Girls': '👧',
    '3D': '🎮',
    'Hypercasual': '📱',
    'Skill': '🎯',
    'Animal': '🐾',
    'Casual': '🎲',
    'Other': '🎪'
  };
  
  return categoryIcons[categoryName] || '🎮';
};

// 暗色模式状态
const isDarkMode = ref(document.documentElement.classList.contains('dark'));

// 切换暗色模式
const toggleDarkMode = () => {
  isDarkMode.value = !isDarkMode.value;
  
  if (isDarkMode.value) {
    document.documentElement.classList.add('dark');
    localStorage.setItem('theme', 'dark');
  } else {
    document.documentElement.classList.remove('dark');
    localStorage.setItem('theme', 'light');
  }
};

// 组件挂载时初始化暗色模式状态
onMounted(() => {
  // 初始化暗色模式状态
  isDarkMode.value = document.documentElement.classList.contains('dark');
  
  // 监听系统主题变化
  const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
  const handleChange = (e) => {
    if (!localStorage.getItem('theme')) {
      isDarkMode.value = e.matches;
      if (isDarkMode.value) {
        document.documentElement.classList.add('dark');
      } else {
        document.documentElement.classList.remove('dark');
      }
    }
  };
  
  mediaQuery.addEventListener('change', handleChange);
});

// 监听路由参数变化，确保分类页面内容更新
watch(() => route.params.name, (newCategoryName) => {
  console.log('Category changed to:', newCategoryName);
  // 路由参数变化时，Vue会自动重新计算依赖于route.params的计算属性
  // 这里可以添加额外的逻辑，如滚动到页面顶部
  window.scrollTo(0, 0);
}, { immediate: true });
</script>

<template>
  <main class="bg-white dark:bg-gray-900 min-h-screen transition-colors duration-300">
    <!-- 顶部导航栏 -->
    <header class="bg-purple-600 dark:bg-purple-800 py-4 flex justify-between items-center px-4 relative">
      <h1 class="text-3xl font-bold text-white cursor-pointer text-center flex-grow" @click="goHome">ZAPPLAYFUN.GAMES</h1>
      <div class="flex items-center">
        <!-- 主题切换按钮 -->
        <button 
          @click="toggleDarkMode" 
          class="text-white p-2 rounded-lg hover:bg-purple-700 dark:hover:bg-purple-900 focus:outline-none focus:ring-2 focus:ring-purple-400"
          aria-label="Toggle dark mode"
        >
          <!-- 月亮图标 (暗色模式) -->
          <svg 
            xmlns="http://www.w3.org/2000/svg" 
            class="h-6 w-6" 
            :class="{'hidden': isDarkMode}"
            fill="none" 
            viewBox="0 0 24 24" 
            stroke="currentColor"
          >
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
          </svg>
          <!-- 太阳图标 (亮色模式) -->
          <svg 
            xmlns="http://www.w3.org/2000/svg" 
            class="h-6 w-6" 
            :class="{'hidden': !isDarkMode}"
            fill="none" 
            viewBox="0 0 24 24" 
            stroke="currentColor"
          >
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
          </svg>
        </button>
      </div>
    </header>
    
    <div class="container mx-auto px-4 py-6">
      <!-- 主要内容区 -->
      <div class="flex flex-col lg:flex-row gap-6">
        <!-- 左侧内容区 - 游戏列表 -->
        <div class="w-full lg:w-3/4">
          <!-- 分类标题 -->
          <div class="flex items-center mb-6">
            <button @click="goBack" class="mr-4 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
              </svg>
            </button>
            <div class="flex items-center">
              <span class="text-4xl mr-3">{{ getCategoryIcon(categoryName) }}</span>
              <h1 class="text-4xl font-bold text-gray-900 dark:text-white">{{ categoryName }}</h1>
            </div>
          </div>
          
          <!-- 分类游戏列表 -->
          <div class="border-t border-gray-200 dark:border-gray-700 pt-6">
            <div v-if="categoryGames.length > 0" class="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-6">
              <a 
                v-for="game in categoryGames" 
                :key="`${categoryName}-${game.id}`" 
                class="flex items-start gap-4 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 p-2 rounded-lg transition-colors"
                @click.prevent="navigateToGameDetail(game.id)"
              >
                <img 
                  :src="game.icon_url" 
                  :alt="game.title" 
                  class="w-24 h-24 object-cover rounded-lg flex-shrink-0"
                />
                <div>
                  <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-1">{{ game.title }}</h3>
                  
                  <!-- 分类标签 -->
                  <div class="flex flex-wrap gap-1 mb-2">
                    <span 
                      v-for="category in game.categories?.slice(0, 2)" 
                      :key="`${game.id}-${category}`"
                      class="bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 text-xs px-2 py-0.5 rounded-full"
                    >
                      {{ category }}
                    </span>
                    <span 
                      v-if="game.categories && game.categories.length > 2" 
                      class="text-xs text-gray-500 dark:text-gray-400"
                    >
                      +{{ game.categories.length - 2 }}
                    </span>
                  </div>
                  
                  <p class="text-sm text-gray-600 dark:text-gray-400 line-clamp-3">{{ game.description }}</p>
                </div>
              </a>
            </div>
            
            <!-- 无游戏提示 -->
            <div v-else class="text-center py-12">
              <p class="text-gray-500 dark:text-gray-400 text-lg">该分类下暂无游戏</p>
              <button 
                @click="goHome" 
                class="mt-4 bg-purple-600 hover:bg-purple-700 text-white font-medium py-2 px-6 rounded-lg transition-colors"
              >
                返回首页
              </button>
            </div>
          </div>
        </div>
        
        <!-- 右侧分类区 -->
        <div class="w-full lg:w-1/4 pt-8">
          <CategoryList />
        </div>
      </div>
      
      <!-- 版权信息 -->
      <div class="mt-12 pt-8 border-t border-gray-200 dark:border-gray-700 text-center text-xs text-gray-500 dark:text-gray-400">
        <p>&copy; {{ new Date().getFullYear() }} ZapPlayFun. All rights reserved.</p>
      </div>
    </div>
  </main>
</template>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style> 