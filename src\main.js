import { createApp } from 'vue'
import './style.css'
import App from './App.vue'
import router from './router'

// 初始化暗色模式
const initDarkMode = () => {
  // 检查本地存储中的主题设置
  const savedTheme = localStorage.getItem('theme');
  const isDarkMode = savedTheme === 'dark' || 
                    (!savedTheme && window.matchMedia('(prefers-color-scheme: dark)').matches);
  
  // 应用主题
  if (isDarkMode) {
    document.documentElement.classList.add('dark');
  } else {
    document.documentElement.classList.remove('dark');
  }
};

// 在应用挂载前初始化暗色模式
initDarkMode();

createApp(App).use(router).mount('#app')
