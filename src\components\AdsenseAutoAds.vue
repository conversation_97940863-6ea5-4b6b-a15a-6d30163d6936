<script setup>
import { onMounted, ref } from 'vue';
import { adsenseConfig, isDevelopment, getAdSenseScriptUrl } from '../config/adsConfig';

// 追踪脚本加载状态
const scriptLoaded = ref(false);

// 加载AdSense脚本
const loadAdsenseScript = () => {
  // 如果已加载或不启用自动广告或处于开发环境，则不加载
  if (scriptLoaded.value || !adsenseConfig.autoAdsEnabled || isDevelopment) {
    return;
  }

  // 创建脚本元素
  const script = document.createElement('script');
  script.async = true;
  script.crossOrigin = 'anonymous';
  script.src = getAdSenseScriptUrl();
  
  // 添加脚本加载成功回调
  script.onload = () => {
    console.log('AdSense script loaded successfully');
    scriptLoaded.value = true;
  };
  
  // 添加脚本加载失败回调
  script.onerror = (error) => {
    console.error('AdSense script failed to load', error);
  };
  
  // 将脚本添加到文档头部
  document.head.appendChild(script);
};

onMounted(() => {
  // 页面加载后延迟加载广告脚本
  setTimeout(() => {
    loadAdsenseScript();
  }, 100);
});
</script>

<template>
  <!-- 自动广告组件不需要模板内容 -->
</template> 