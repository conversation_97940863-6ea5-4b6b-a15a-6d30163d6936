<script setup>
import { ref, onMounted, computed, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { games } from '../data/games.js';

const router = useRouter();
const route = useRoute();

// 分类图标映射
const categoryIcons = {
  'Racing': '🏎️',
  'Action': '💥',
  'Adventure': '🗺️',
  'Arcade': '🕹️',
  'Puzzle': '🧩',
  'Shooting': '🔫',
  'Girls': '👧',
  '3D': '🎮',
  'Hypercasual': '📱',
  'Skill': '🎯',
  'Animal': '🐾',
  'Casual': '🎲',
  'Other': '🎪'
};

// 计算所有游戏的分类
const categories = ref([]);

// 统计分类数量
const countCategories = () => {
  const categoryCounts = {};
  
  // 使用完整的games数组
  games.forEach(game => {
    if (game.categories && Array.isArray(game.categories)) {
      game.categories.forEach(category => {
        if (!categoryCounts[category]) {
          categoryCounts[category] = 0;
        }
        categoryCounts[category]++;
      });
    }
  });
  
  // 转换为数组并排序
  const sortedCategories = Object.keys(categoryCounts).map(name => ({
    name,
    count: categoryCounts[name]
  })).sort((a, b) => b.count - a.count);
  
  // 获取前5个分类
  const topCategories = sortedCategories.slice(0, 5);
  
  // 获取前5个分类的名称
  const topCategoryNames = topCategories.map(cat => cat.name);
  
  // 计算其他分类的游戏数量
  // 找出不在前5个分类中的游戏
  let otherGamesCount = 0;
  games.forEach(game => {
    // 如果游戏没有分类或者分类不是数组，则计入其他
    if (!game.categories || !Array.isArray(game.categories) || game.categories.length === 0) {
      otherGamesCount++;
      return;
    }
    
    // 检查游戏的所有分类是否都不在主要分类中
    const isInMainCategory = game.categories.some(cat => topCategoryNames.includes(cat));
    if (!isInMainCategory) {
      otherGamesCount++;
    }
  });
  
  // 如果有其他分类，添加到列表中
  if (otherGamesCount > 0) {
    topCategories.push({
      name: 'Other',
      count: otherGamesCount
    });
  }
  
  categories.value = topCategories;
};

// 组件挂载时统计分类
onMounted(() => {
  countCategories();
});

// 获取分类图标
const getCategoryIcon = (categoryName) => {
  return categoryIcons[categoryName] || '🎮';
};

// 获取分类背景颜色
const getCategoryBgColor = (categoryName) => {
  const colorMap = {
    'Racing': 'bg-red-100 dark:bg-red-900',
    'Action': 'bg-orange-100 dark:bg-orange-900',
    'Adventure': 'bg-yellow-100 dark:bg-yellow-900',
    'Arcade': 'bg-green-100 dark:bg-green-900',
    'Puzzle': 'bg-blue-100 dark:bg-blue-900',
    'Shooting': 'bg-indigo-100 dark:bg-indigo-900',
    'Girls': 'bg-pink-100 dark:bg-pink-900',
    '3D': 'bg-purple-100 dark:bg-purple-900',
    'Hypercasual': 'bg-teal-100 dark:bg-teal-900',
    'Skill': 'bg-cyan-100 dark:bg-cyan-900',
    'Animal': 'bg-emerald-100 dark:bg-emerald-900',
    'Casual': 'bg-sky-100 dark:bg-sky-900',
    'Other': 'bg-gray-100 dark:bg-gray-800'
  };
  
  return colorMap[categoryName] || 'bg-gray-100 dark:bg-gray-800';
};

// 导航到分类页面
const navigateToCategory = (categoryName) => {
  // 如果当前已经在分类页面，且点击了相同的分类，需要强制刷新
  if (route.name === 'category' && route.params.name === categoryName) {
    // 先导航到一个临时路径，然后再导航回来，强制刷新组件
    router.push('/').then(() => {
      router.push(`/category/${categoryName}`);
    });
  } else {
    // 正常导航到分类页面
    router.push(`/category/${categoryName}`);
  }
};

// 当前选中的分类
const currentCategory = computed(() => {
  return route.name === 'category' ? route.params.name : null;
});
</script>

<template>
  <div class="category-list">
    <h2 class="text-2xl font-bold text-gray-900 dark:text-white pb-2 mb-4 border-b border-gray-200 dark:border-gray-700">
      Category
    </h2>
    
    <ul class="flex flex-col gap-3">
      <li v-for="category in categories" :key="category.name">
        <a 
          @click.prevent="navigateToCategory(category.name)" 
          href="#" 
          class="flex items-center p-2 rounded-lg transition-colors"
          :class="[
            currentCategory === category.name 
              ? 'bg-gray-100 dark:bg-gray-800' 
              : 'hover:bg-gray-50 dark:hover:bg-gray-800'
          ]"
        >
          <!-- 左侧图标 -->
          <div :class="[getCategoryBgColor(category.name), 'w-10 h-10 rounded-md flex items-center justify-center']">
            <span class="text-lg">{{ getCategoryIcon(category.name) }}</span>
          </div>
          
          <!-- 中间文本 -->
          <div class="ml-3 flex-grow">
            <span class="font-semibold text-gray-800 dark:text-gray-100">{{ category.name }}</span>
            <p class="text-sm text-gray-500 dark:text-gray-400">{{ category.count }} games</p>
          </div>
          
          <!-- 右侧箭头 -->
          <div class="ml-auto text-gray-400">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
            </svg>
          </div>
        </a>
      </li>
    </ul>
  </div>
</template> 