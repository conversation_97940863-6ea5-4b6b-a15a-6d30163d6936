#!/usr/bin/env python
# -*- coding: utf-8 -*-

import requests
from bs4 import BeautifulSoup
import json
import os
import time
import re
from urllib.parse import urljoin
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 基础URL
BASE_URL = "https://gamemonetize.com"
GAMES_URL = "https://gamemonetize.com/games"

# 分类URL参数
CATEGORIES = {
    "best_games": "popularity=1",  # Best Games
    "hot_games": "popularity=0"    # Hot Games
}

def fetch_page(url, retries=3, delay=1, params=None):
    """
    获取网页内容
    
    Args:
        url (str): 要抓取的URL
        retries (int): 重试次数
        delay (float): 重试延迟(秒)
        params (dict): URL参数
    
    Returns:
        str: 网页内容
    """
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
        'Referer': BASE_URL
    }
    
    for attempt in range(retries):
        try:
            logger.info(f"正在获取页面: {url} (尝试 {attempt+1}/{retries})")
            response = requests.get(url, headers=headers, params=params)
            response.raise_for_status()
            return response.text
        except requests.RequestException as e:
            logger.error(f"获取页面失败: {e}")
            if attempt < retries - 1:
                logger.info(f"等待 {delay} 秒后重试...")
                time.sleep(delay)
            else:
                logger.error(f"已达到最大重试次数，放弃获取页面: {url}")
                return None

def parse_html(html_content):
    """
    解析HTML内容
    
    Args:
        html_content (str): HTML内容
    
    Returns:
        BeautifulSoup: 解析后的BeautifulSoup对象
    """
    if html_content:
        return BeautifulSoup(html_content, 'lxml')
    return None

def extract_game_links_from_category_page(soup):
    """
    从分类页面提取游戏链接
    
    Args:
        soup (BeautifulSoup): 解析后的页面
    
    Returns:
        list: 游戏链接列表
    """
    game_links = []
    try:
        # 查找所有游戏链接，根据demo.html中的结构
        game_elements = soup.select('a.game-link')
        if not game_elements:
            # 尝试其他可能的选择器
            game_elements = soup.select('.game-item a') or soup.select('.game a') or soup.select('a[href*="-game"]')
        
        for game_elem in game_elements:
            href = game_elem.get('href')
            if href and "-game" in href:
                full_url = urljoin(BASE_URL, href)
                game_links.append(full_url)
                
        # 去重
        game_links = list(set(game_links))
    except Exception as e:
        logger.error(f"提取游戏链接时出错: {e}")
    
    return game_links

def extract_game_info(game_url):
    """
    从游戏详情页提取游戏信息
    
    Args:
        game_url (str): 游戏详情页URL
    
    Returns:
        dict: 游戏信息字典
    """
    html_content = fetch_page(game_url)
    if not html_content:
        logger.error(f"无法获取游戏详情页: {game_url}")
        return None
    
    soup = parse_html(html_content)
    if not soup:
        return None
    
    game_info = {
        'url': game_url,
        'id': game_url.split('/')[-1].replace('-game', '')
    }
    
    try:
        # 提取标题 - 首先尝试h1标签，然后是h2标签
        title_elem = soup.select_one('h1')
        if not title_elem:
            title_elem = soup.select_one('h2')
        
        if title_elem:
            game_info['title'] = title_elem.text.strip()
        else:
            # 备用方法
            title_elem = soup.select_one('title')
            if title_elem:
                title = title_elem.text.strip()
                # 移除网站名称部分
                title = re.sub(r'\s*\|\s*.*$', '', title)
                game_info['title'] = title
        
        # 提取图标 - 尝试多种可能的选择器
        # 查找下载区域的图片
        download_section = soup.select_one('.thumbnail') or soup.select_one('div:contains("Download")')
        if download_section:
            img_elems = download_section.select('img')
            if img_elems:
                game_info['icon_url'] = urljoin(BASE_URL, img_elems[0].get('src', ''))
        
        # 如果上面的方法没找到，尝试其他选择器
        if 'icon_url' not in game_info:
            for selector in ['img[alt*="Download"]', 'img.game-image', '.game-image img', 'img[src*="thumbnail"]']:
                icon_elem = soup.select_one(selector)
                if icon_elem:
                    game_info['icon_url'] = urljoin(BASE_URL, icon_elem.get('src', ''))
                    break
        
        # 提取描述 - 使用提供的HTML结构优化提取逻辑
        # 方法1: 寻找id为descriptionId的元素
        description = ""
        desc_elem = soup.select_one('#descriptionId')
        if desc_elem:
            description = desc_elem.text.strip()
        
        # 方法2: 如果方法1未找到，寻找带有"Description"文本的卡片后面的p元素
        if not description:
            desc_card = soup.select_one('.unit-card:contains("Description")')
            if desc_card:
                # 查找下一个p元素
                desc_p = desc_card.find_next('p')
                if desc_p:
                    description = desc_p.text.strip()
        
        # 方法3: 如果前两种方法都未找到，尝试其他选择器
        if not description:
            desc_section = soup.select_one('.description') or soup.select_one('.gamedesc')
            if desc_section:
                # 尝试找到描述文本，移除"Read more.."等无关文本
                desc_text = desc_section.text.strip()
                desc_text = re.sub(r'Read more\.\.', '', desc_text).strip()
                if desc_text:
                    description = desc_text
        
        # 方法4: 查找所有可能包含描述的元素
        if not description:
            for selector in ['.text-overflow', 'p.gamedesc', '.game-description', 'div[itemprop="description"]']:
                desc_elem = soup.select_one(selector)
                if desc_elem:
                    description = desc_elem.text.strip()
                    break
        
        # 保存描述
        if description:
            game_info['description'] = description
        
        # 提取分类 - 使用提供的HTML结构优化提取逻辑
        # 方法1: 寻找带有"Category"文本的卡片后面的ul.filters
        categories = []
        category_card = soup.select_one('.unit-card:contains("Category")')
        if category_card:
            # 查找下一个ul.filters元素
            filters_ul = category_card.find_next('ul', class_='filters')
            if filters_ul:
                category_links = filters_ul.select('li a')
                categories = [link.text.strip() for link in category_links]
        
        # 方法2: 如果方法1未找到，尝试其他选择器
        if not categories:
            # 尝试直接查找类别部分
            category_section = soup.select_one('.category') or soup.select_one('div:contains("Category")')
            if category_section:
                category_links = category_section.select('a')
                if category_links:
                    categories = [link.text.strip() for link in category_links]
                else:
                    # 尝试查找邻近的ul元素
                    filters_ul = category_section.find_next('ul')
                    if filters_ul:
                        category_links = filters_ul.select('li a')
                        categories = [link.text.strip() for link in category_links]
        
        # 方法3: 尝试查找所有包含category参数的链接
        if not categories:
            category_links = soup.select('a[href*="category="]')
            if category_links:
                categories = [link.text.strip() for link in category_links]
        
        if categories:
            game_info['categories'] = categories
        
        # 提取游戏链接 - 首先尝试iframe，然后是带有特定类的链接
        iframe_elem = soup.select_one('iframe')
        if iframe_elem and iframe_elem.get('src'):
            game_info['external_play_url'] = iframe_elem.get('src')
        else:
            # 尝试找到带有play按钮的链接
            play_link = None
            for selector in ['a.play-button', 'a.play', 'a[href*="html5.gamemonetize"]', 'a:contains("Open")']:
                play_link = soup.select_one(selector)
                if play_link:
                    break
            
            if play_link:
                game_info['external_play_url'] = play_link.get('href', '')
        
        # 提取标签
        tag_section = soup.select_one('.tags')
        if tag_section:
            tag_links = tag_section.select('a')
            if not tag_links:
                # 如果没有链接，尝试直接获取文本
                tags_text = tag_section.text.strip()
                tags = [tag.strip() for tag in tags_text.split('\n') if tag.strip()]
                if tags:
                    game_info['tags'] = tags
            else:
                tags = [link.text.strip() for link in tag_links]
                game_info['tags'] = tags
        
        # 提取游戏尺寸（如果有）
        size_section = soup.select_one('.size')
        if size_section:
            size_text = size_section.text.strip()
            # 只保留尺寸信息
            match = re.search(r'(\d+)\s*X\s*(\d+)', size_text, re.IGNORECASE)
            if match:
                game_info['size'] = f"{match.group(1)}x{match.group(2)}"
        
        # 提取游戏发布日期（如果有）
        published_section = soup.select_one('.published')
        if published_section:
            published_text = published_section.text.strip()
            # 只保留日期信息
            match = re.search(r'(Mon|Tue|Wed|Thu|Fri|Sat|Sun)\s+(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s+(\d{1,2})\s+(\d{4})', published_text)
            if match:
                game_info['published_date'] = f"{match.group(2)} {match.group(3)}, {match.group(4)}"
        
        # 提取游戏开发者（如果有）
        developer_text = None
        by_text = soup.select_one('div:contains("by ")')
        if by_text:
            developer_text = by_text.text.strip()
        else:
            # 尝试查找标题下方的开发者信息
            title_elem = soup.select_one('h2')
            if title_elem:
                next_elem = title_elem.find_next_sibling()
                if next_elem and 'by' in next_elem.text:
                    developer_text = next_elem.text.strip()
        
        if developer_text:
            match = re.search(r'by\s+(.+?)(?:\s*$|\s*\n)', developer_text, re.IGNORECASE)
            if match:
                game_info['developer'] = match.group(1).strip()
        
        # 提取移动端适配信息
        mobile_ready_section = soup.select_one('div:contains("Mobile Ready")') 
        if mobile_ready_section:
            mobile_text = mobile_ready_section.text.strip()
            match = re.search(r'Mobile Ready\s*:\s*(Yes|No)', mobile_text, re.IGNORECASE)
            if match:
                game_info['mobile_ready'] = match.group(1) == 'Yes'
        
    except Exception as e:
        logger.error(f"提取游戏信息时出错: {e}")
    
    return game_info

def scrape_category(category_name, category_param, max_games=10, max_pages=1):
    """
    爬取特定分类下的游戏信息
    
    Args:
        category_name (str): 分类名称
        category_param (str): 分类URL参数
        max_games (int): 最大爬取游戏数量
        max_pages (int): 最大爬取页面数
    
    Returns:
        list: 游戏信息列表
    """
    logger.info(f"开始爬取 {category_name} 分类下的游戏...")
    
    all_game_links = []
    games_data = []
    
    # 爬取多页游戏链接
    for page in range(1, max_pages + 1):
        params = {
            'page': page
        }
        
        # 添加分类参数
        if '=' in category_param:
            param_name, param_value = category_param.split('=')
            params[param_name] = param_value
        
        html_content = fetch_page(GAMES_URL, params=params)
        if not html_content:
            logger.error(f"无法获取 {category_name} 分类第 {page} 页")
            continue
        
        soup = parse_html(html_content)
        if not soup:
            logger.error(f"无法解析 {category_name} 分类第 {page} 页")
            continue
        
        game_links = extract_game_links_from_category_page(soup)
        logger.info(f"在 {category_name} 分类第 {page} 页找到 {len(game_links)} 个游戏链接")
        
        all_game_links.extend(game_links)
        
        # 如果已经找到足够多的游戏链接，就停止爬取
        if len(all_game_links) >= max_games:
            break
    
    # 去重并限制数量
    all_game_links = list(set(all_game_links))[:max_games]
    logger.info(f"总共找到 {len(all_game_links)} 个游戏链接")
    
    # 创建数据目录
    os.makedirs('data', exist_ok=True)
    
    # 提取每个游戏的详细信息
    for i, game_url in enumerate(all_game_links):
        logger.info(f"正在处理游戏 {i+1}/{len(all_game_links)}: {game_url}")
        game_info = extract_game_info(game_url)
        if game_info:
            games_data.append(game_info)
            logger.info(f"成功提取游戏信息: {game_info.get('title', '未知')}")
        
        # 添加延迟，避免请求过于频繁
        if i < len(all_game_links) - 1:
            time.sleep(1)
    
    # 保存数据到JSON文件
    output_file = os.path.join('data', f"{category_name}.json")
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(games_data, f, ensure_ascii=False, indent=2)
    
    logger.info(f"成功爬取 {len(games_data)} 个游戏信息，数据已保存到 {output_file}")
    
    return games_data

def main():
    """主函数"""
    # 爬取Best Games分类
    best_games = scrape_category("best_games", CATEGORIES["best_games"], max_games=100)
    
    # 爬取Hot Games分类
    hot_games = scrape_category("hot_games", CATEGORIES["hot_games"], max_games=100)
    
    # 合并数据并去重
    all_games = best_games + hot_games
    unique_games = {}
    for game in all_games:
        if game['id'] not in unique_games:
            unique_games[game['id']] = game
    
    # 保存合并后的数据
    output_file = os.path.join('data', "all_popular_games.json")
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(list(unique_games.values()), f, ensure_ascii=False, indent=2)
    
    logger.info(f"成功爬取并合并 {len(unique_games)} 个游戏信息，数据已保存到 {output_file}")

if __name__ == "__main__":
    main() 