<script setup>
import { ref, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { games } from '../data/games.js';

const route = useRoute();
const router = useRouter();
const gameId = route.params.id;

const game = computed(() => {
  return games.find(g => g.id === gameId) || null;
});

const goBack = () => {
  router.push('/');
};

// 如果找不到游戏，显示错误信息
const notFound = computed(() => {
  return !game.value;
});
</script>

<template>
  <main class="bg-gray-100 min-h-screen">
    <!-- 顶部导航栏 -->
    <header class="bg-purple-600 py-4 shadow-md">
      <div class="container mx-auto px-4 flex justify-between items-center">
        <h1 class="text-3xl font-bold text-white">ZAPPLAYFUN.GAMES</h1>
        <button @click="goBack" class="text-white hover:text-gray-200">
          返回首页
        </button>
      </div>
    </header>
    
    <div v-if="notFound" class="container mx-auto px-4 py-12 text-center">
      <h2 class="text-2xl font-bold text-red-600 mb-4">游戏未找到</h2>
      <p class="text-gray-600 mb-6">抱歉，我们找不到您请求的游戏。</p>
      <button @click="goBack" class="bg-purple-600 text-white px-6 py-2 rounded-lg hover:bg-purple-700">
        返回首页
      </button>
    </div>
    
    <div v-else class="container mx-auto px-4 py-6">
      <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <!-- 游戏基本信息 -->
        <div class="md:flex">
          <div class="md:w-1/3 p-6">
            <img :src="game.icon_url" :alt="game.title" class="w-full h-auto object-cover rounded-lg" />
          </div>
          <div class="md:w-2/3 p-6">
            <h1 class="text-3xl font-bold text-gray-800 mb-2">{{ game.title }}</h1>
            
            <div class="flex flex-wrap gap-2 mb-4">
              <span 
                v-for="category in game.categories" 
                :key="category"
                class="bg-indigo-100 text-indigo-800 text-xs px-3 py-1 rounded-full"
              >
                {{ category }}
              </span>
            </div>
            
            <div class="mb-6">
              <h2 class="text-xl font-semibold text-gray-700 mb-2">游戏描述</h2>
              <p class="text-gray-600">{{ game.description }}</p>
            </div>
            
            <div class="flex flex-col sm:flex-row gap-4">
              <a 
                :href="game.external_play_url" 
                target="_blank" 
                rel="noopener noreferrer"
                class="bg-green-600 text-white px-6 py-3 rounded-lg text-center hover:bg-green-700 transition-colors"
              >
                立即游戏
              </a>
              <button 
                @click="goBack" 
                class="border border-gray-300 text-gray-700 px-6 py-3 rounded-lg text-center hover:bg-gray-100 transition-colors"
              >
                返回首页
              </button>
            </div>
          </div>
        </div>
        
        <!-- 游戏详细信息 -->
        <div class="border-t border-gray-200 p-6">
          <h2 class="text-xl font-semibold text-gray-700 mb-4">详细信息</h2>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <p class="text-gray-600"><span class="font-medium">开发者:</span> {{ game.developer || '未知' }}</p>
            </div>
            <div>
              <p class="text-gray-600"><span class="font-medium">移动端支持:</span> {{ game.mobile_ready ? '是' : '否' }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </main>
</template> 