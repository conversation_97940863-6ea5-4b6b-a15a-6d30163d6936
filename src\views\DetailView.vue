<script setup>
import { ref, onMounted, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { games } from '../data/games.js';

const route = useRoute();
const router = useRouter();

// 获取当前游戏ID
const gameId = computed(() => route.params.id);

// 获取游戏详情
const game = computed(() => {
  return games.find(g => g.id === gameId.value) || null;
});

// 返回上一页
const goBack = () => {
  router.go(-1);
};

// 返回首页
const goHome = () => {
  router.push('/');
};

// 暗色模式状态
const isDarkMode = ref(document.documentElement.classList.contains('dark'));

// 切换暗色模式
const toggleDarkMode = () => {
  isDarkMode.value = !isDarkMode.value;
  
  if (isDarkMode.value) {
    document.documentElement.classList.add('dark');
    localStorage.setItem('theme', 'dark');
  } else {
    document.documentElement.classList.remove('dark');
    localStorage.setItem('theme', 'light');
  }
};

// 组件挂载时初始化暗色模式状态
onMounted(() => {
  // 初始化暗色模式状态
  isDarkMode.value = document.documentElement.classList.contains('dark');
  
  // 监听系统主题变化
  const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
  const handleChange = (e) => {
    if (!localStorage.getItem('theme')) {
      isDarkMode.value = e.matches;
      if (isDarkMode.value) {
        document.documentElement.classList.add('dark');
      } else {
        document.documentElement.classList.remove('dark');
      }
    }
  };
  
  mediaQuery.addEventListener('change', handleChange);
});
</script>

<template>
  <main class="bg-white dark:bg-gray-900 min-h-screen transition-colors duration-300">
    <!-- 顶部导航栏 -->
    <header class="bg-purple-600 dark:bg-purple-800 py-4 flex justify-between items-center px-4 relative">
      <h1 class="text-3xl font-bold text-white cursor-pointer text-center flex-grow" @click="goHome">ZAPPLAYFUN.GAMES</h1>
      <div class="flex items-center">
        <!-- 主题切换按钮 -->
        <button 
          @click="toggleDarkMode" 
          class="text-white p-2 rounded-lg hover:bg-purple-700 dark:hover:bg-purple-900 focus:outline-none focus:ring-2 focus:ring-purple-400"
          aria-label="Toggle dark mode"
        >
          <!-- 月亮图标 (暗色模式) -->
          <svg 
            xmlns="http://www.w3.org/2000/svg" 
            class="h-6 w-6" 
            :class="{'hidden': isDarkMode}"
            fill="none" 
            viewBox="0 0 24 24" 
            stroke="currentColor"
          >
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
          </svg>
          <!-- 太阳图标 (亮色模式) -->
          <svg 
            xmlns="http://www.w3.org/2000/svg" 
            class="h-6 w-6" 
            :class="{'hidden': !isDarkMode}"
            fill="none" 
            viewBox="0 0 24 24" 
            stroke="currentColor"
          >
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
          </svg>
        </button>
      </div>
    </header>
    
    <div class="container mx-auto px-4 py-6">
      <!-- 返回按钮 -->
      <button @click="goBack" class="mb-6 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white flex items-center">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
        </svg>
        Back
      </button>
      
      <!-- 游戏详情 -->
      <div v-if="game" class="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden">
        <!-- 游戏封面图 -->
        <div class="relative h-64 md:h-96">
          <img :src="game.icon_url" :alt="game.title" class="w-full h-full object-cover" />
          <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-6">
            <h1 class="text-3xl md:text-4xl font-bold text-white">{{ game.title }}</h1>
            <div class="flex flex-wrap gap-2 mt-2">
              <span 
                v-for="category in game.categories" 
                :key="category"
                class="bg-gray-800 bg-opacity-50 text-white text-xs px-2 py-1 rounded-full"
              >
                {{ category }}
              </span>
            </div>
          </div>
        </div>
        
        <!-- 游戏信息 -->
        <div class="p-6">
          <div class="flex flex-wrap justify-between items-center mb-6">
            <div>
              <p class="text-sm text-gray-500 dark:text-gray-400">Developer: {{ game.developer || 'Unknown' }}</p>
              <p class="text-sm text-gray-500 dark:text-gray-400">Mobile Support: {{ game.mobile_ready ? 'Yes' : 'No' }}</p>
            </div>
            <a 
              :href="game.external_play_url" 
              target="_blank" 
              class="bg-green-500 hover:bg-green-600 text-white font-medium py-2 px-6 rounded-lg transition-colors"
            >
              Play Now
            </a>
          </div>
          
          <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-3">Game Description</h2>
          <p class="text-gray-700 dark:text-gray-300 whitespace-pre-line">{{ game.description }}</p>
        </div>
      </div>
      
      <!-- 游戏不存在提示 -->
      <div v-else class="text-center py-12">
        <p class="text-gray-500 dark:text-gray-400 text-lg mb-4">Game not found</p>
        <button 
          @click="goBack" 
          class="bg-purple-600 hover:bg-purple-700 text-white font-medium py-2 px-6 rounded-lg transition-colors"
        >
          Back
        </button>
      </div>
      
      <!-- 版权信息 -->
      <div class="mt-12 pt-8 border-t border-gray-200 dark:border-gray-700 text-center text-xs text-gray-500 dark:text-gray-400">
        <p>&copy; {{ new Date().getFullYear() }} ZapPlayFun. All rights reserved.</p>
      </div>
    </div>
  </main>
</template> 