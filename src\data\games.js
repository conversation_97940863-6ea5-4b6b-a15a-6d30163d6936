// We use .js instead of .json to allow for comments and easier importing.
import gamesData from './hot_games.json';

// 处理爬虫数据，确保每个游戏都有必要的字段
const processGameData = (games) => {
  return games.map(game => {
    // 直接使用原始图标URL，不进行修改
    const iconUrl = game.icon_url || '/images/icons/default-game.png';
    
    return {
      id: game.id || `game-${Math.random().toString(36).substring(2, 9)}`,
      title: game.title || 'Unknown Game',
      description: game.description || 'No description available',
      icon_url: iconUrl,
      categories: game.categories || [],
      external_play_url: game.external_play_url || '#',
      developer: game.developer || 'Unknown Developer',
      mobile_ready: game.mobile_ready || false
    };
  });
};

// 处理所有游戏数据
const allProcessedGames = processGameData(gamesData);

// 分别导出热门游戏(前10个)和最佳游戏(接下来的10个)
export const hotGames = allProcessedGames.slice(0, 10);
export const bestGames = allProcessedGames.slice(10, 20);

// 导出所有游戏数据
export const games = allProcessedGames;

// 提取所有游戏的分类
export const categories = [...new Set(
  games.flatMap(game => game.categories || [])
)].sort();