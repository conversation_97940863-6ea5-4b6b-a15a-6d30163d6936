<script setup>
import { onMounted, ref } from 'vue';
import { adsenseConfig, adUnitConfig, isDevelopment } from '../config/adsConfig';

// 广告加载后执行
onMounted(() => {
  // 只在生产环境中且手动广告启用时初始化AdSense
  if (!isDevelopment && adsenseConfig.manualAdsEnabled) {
    // 延迟一小段时间确保DOM已完全渲染
    setTimeout(() => {
      try {
        // 确保全局的adsbygoogle对象存在
        if (window.adsbygoogle && window.adsbygoogle.push) {
          window.adsbygoogle.push({});
        }
      } catch (error) {
        console.error('AdSense initialization error:', error);
      }
    }, 100);
  }
});
</script>

<template>
  <div class="ad-container my-4">
    <!-- 生产环境显示真实广告 -->
    <ins v-if="!isDevelopment && adsenseConfig.manualAdsEnabled" 
         class="adsbygoogle"
         :style="adUnitConfig.banner.adStyle"
         :data-ad-client="adsenseConfig.client"
         :data-ad-slot="adUnitConfig.banner.adSlot"
         :data-ad-format="adUnitConfig.banner.adFormat"></ins>
    
    <!-- 开发环境显示占位符 -->
    <div v-else class="ad-placeholder">
      <span>Ad Space</span>
      <span class="ad-size">320 x 250</span>
    </div>
  </div>
</template>

<style scoped>
.ad-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  overflow: hidden;
  background-color: #f9fafb;
  border-radius: 8px;
  padding: 8px 0;
  min-height: 250px;
  max-height: 270px;
}

.dark .ad-container {
  background-color: #1f2937;
}

/* 广告占位符样式 */
.ad-placeholder {
  width: 320px;
  height: 250px;
  border: 2px dashed #ccc;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #888;
  font-size: 18px;
  background-color: rgba(200, 200, 200, 0.1);
}

.ad-size {
  font-size: 14px;
  margin-top: 8px;
  opacity: 0.7;
}

@media (max-width: 350px) {
  .adsbygoogle, .ad-placeholder {
    width: 300px;
    height: 250px;
  }
}
</style> 