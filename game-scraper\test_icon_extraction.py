#!/usr/bin/env python
# -*- coding: utf-8 -*-

import requests
from bs4 import BeautifulSoup
import logging
from urllib.parse import urljoin

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 基础URL
BASE_URL = "https://gamemonetize.com"

def test_icon_extraction(game_id):
    """
    测试从指定游戏ID提取icon URL
    
    Args:
        game_id (str): 游戏ID
    """
    # 构建游戏详情页URL
    game_url = f"{BASE_URL}/{game_id}-game"
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
        'Referer': BASE_URL
    }
    
    try:
        logger.info(f"正在访问: {game_url}")
        response = requests.get(game_url, headers=headers, timeout=10)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # 查找screenshot div
        screenshot_div = soup.select_one('div.screenshot')
        if not screenshot_div:
            logger.error("未找到 div.screenshot")
            return None
        
        logger.info("✓ 找到 div.screenshot")
        
        # 查找所有openimg div
        openimg_divs = screenshot_div.select('div.openimg')
        logger.info(f"找到 {len(openimg_divs)} 个 div.openimg")
        
        if not openimg_divs:
            logger.error("未找到任何 div.openimg")
            return None
        
        # 获取第一个openimg div中的img标签
        first_openimg = openimg_divs[0]
        img_tag = first_openimg.select_one('img')
        
        if not img_tag:
            logger.error("第一个 div.openimg 中未找到 img 标签")
            return None
        
        # 获取src属性
        icon_url = img_tag.get('src')
        if icon_url:
            # 确保URL是完整的
            full_icon_url = urljoin(BASE_URL, icon_url)
            logger.info(f"✓ 成功提取第一个icon URL: {full_icon_url}")
            
            # 显示所有找到的图片URL
            logger.info("所有找到的图片URL:")
            for i, div in enumerate(openimg_divs, 1):
                img = div.select_one('img')
                if img and img.get('src'):
                    url = urljoin(BASE_URL, img.get('src'))
                    logger.info(f"  图片 {i}: {url}")
            
            return full_icon_url
        else:
            logger.error("img标签没有src属性")
            return None
            
    except requests.RequestException as e:
        logger.error(f"请求失败: {e}")
        return None
    except Exception as e:
        logger.error(f"解析失败: {e}")
        return None

def main():
    """主函数 - 测试指定游戏ID"""
    # 测试游戏ID
    test_game_id = "escape-from-school-hellish-teacher"
    
    logger.info(f"开始测试游戏ID: {test_game_id}")
    result = test_icon_extraction(test_game_id)
    
    if result:
        logger.info(f"✓ 测试成功！提取到的icon URL: {result}")
    else:
        logger.error("✗ 测试失败！")

if __name__ == "__main__":
    main()
