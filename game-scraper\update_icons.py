#!/usr/bin/env python
# -*- coding: utf-8 -*-

import requests
from bs4 import BeautifulSoup
import json
import logging
import time
import os
from urllib.parse import urljoin

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 基础URL
BASE_URL = "https://gamemonetize.com"

def fetch_page(url, retries=3, delay=1):
    """
    获取网页内容
    
    Args:
        url (str): 要抓取的URL
        retries (int): 重试次数
        delay (float): 重试延迟(秒)
    
    Returns:
        str: 网页内容
    """
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
        'Referer': BASE_URL
    }
    
    for attempt in range(retries):
        try:
            logger.info(f"正在获取页面: {url} (尝试 {attempt+1}/{retries})")
            response = requests.get(url, headers=headers, timeout=10)
            response.raise_for_status()
            return response.text
        except requests.RequestException as e:
            logger.error(f"获取页面失败: {e}")
            if attempt < retries - 1:
                logger.info(f"等待 {delay} 秒后重试...")
                time.sleep(delay)
            else:
                logger.error(f"已达到最大重试次数，放弃获取页面: {url}")
                return None

def extract_first_icon_url(game_id):
    """
    从游戏详情页提取第一个icon URL
    
    Args:
        game_id (str): 游戏ID
    
    Returns:
        str: 第一个icon的URL，如果失败返回None
    """
    # 构建游戏详情页URL
    game_url = f"{BASE_URL}/{game_id}-game"
    
    html_content = fetch_page(game_url)
    if not html_content:
        logger.error(f"无法获取游戏详情页: {game_url}")
        return None
    
    soup = BeautifulSoup(html_content, 'html.parser')
    
    try:
        # 查找screenshot div
        screenshot_div = soup.select_one('div.screenshot')
        if not screenshot_div:
            logger.warning(f"未找到screenshot div: {game_url}")
            return None
        
        # 查找第一个openimg div中的img标签
        first_openimg = screenshot_div.select_one('div.openimg')
        if not first_openimg:
            logger.warning(f"未找到openimg div: {game_url}")
            return None
        
        # 查找img标签
        img_tag = first_openimg.select_one('img')
        if not img_tag:
            logger.warning(f"未找到img标签: {game_url}")
            return None
        
        # 获取src属性
        icon_url = img_tag.get('src')
        if icon_url:
            # 确保URL是完整的
            icon_url = urljoin(BASE_URL, icon_url)
            logger.info(f"成功提取icon URL: {icon_url}")
            return icon_url
        else:
            logger.warning(f"img标签没有src属性: {game_url}")
            return None
            
    except Exception as e:
        logger.error(f"提取icon URL时出错: {e}")
        return None

def update_game_icons(json_file_path):
    """
    更新JSON文件中所有游戏的icon_url
    
    Args:
        json_file_path (str): JSON文件路径
    """
    # 读取JSON文件
    try:
        with open(json_file_path, 'r', encoding='utf-8') as f:
            games_data = json.load(f)
        logger.info(f"成功读取JSON文件，共 {len(games_data)} 个游戏")
    except Exception as e:
        logger.error(f"读取JSON文件失败: {e}")
        return
    
    # 创建备份文件
    backup_file = json_file_path + '.backup'
    try:
        with open(backup_file, 'w', encoding='utf-8') as f:
            json.dump(games_data, f, ensure_ascii=False, indent=2)
        logger.info(f"已创建备份文件: {backup_file}")
    except Exception as e:
        logger.error(f"创建备份文件失败: {e}")
        return
    
    updated_count = 0
    failed_count = 0
    
    # 遍历所有游戏
    for i, game in enumerate(games_data):
        game_id = game.get('id')
        if not game_id:
            logger.warning(f"游戏 {i+1} 没有ID，跳过")
            failed_count += 1
            continue
        
        logger.info(f"正在处理游戏 {i+1}/{len(games_data)}: {game.get('title', '未知')} (ID: {game_id})")
        
        # 提取新的icon URL
        new_icon_url = extract_first_icon_url(game_id)
        
        if new_icon_url:
            old_icon_url = game.get('icon_url', '')
            game['icon_url'] = new_icon_url
            updated_count += 1
            logger.info(f"✓ 更新成功: {old_icon_url} -> {new_icon_url}")
        else:
            failed_count += 1
            logger.error(f"✗ 更新失败: {game.get('title', '未知')}")
        
        # 添加延迟，避免请求过于频繁
        if i < len(games_data) - 1:
            time.sleep(2)  # 2秒延迟
    
    # 保存更新后的数据
    try:
        with open(json_file_path, 'w', encoding='utf-8') as f:
            json.dump(games_data, f, ensure_ascii=False, indent=2)
        logger.info(f"✓ 成功保存更新后的数据到: {json_file_path}")
        logger.info(f"📊 更新统计: 成功 {updated_count} 个，失败 {failed_count} 个")
    except Exception as e:
        logger.error(f"保存文件失败: {e}")

def main():
    """主函数"""
    # JSON文件路径
    json_file_path = os.path.join('data', 'hot_games.json')
    
    if not os.path.exists(json_file_path):
        logger.error(f"JSON文件不存在: {json_file_path}")
        return
    
    logger.info("开始更新游戏icon URLs...")
    update_game_icons(json_file_path)
    logger.info("更新完成！")

if __name__ == "__main__":
    main()
