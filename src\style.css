@tailwind base;
@tailwind components;
@tailwind utilities;

/* 确保暗色模式样式应用于整个页面 */
:root {
  --bg-color: white;
  --text-color: #111827;
  --border-color: #e5e7eb;
}

.dark {
  --bg-color: #111827;
  --text-color: #f9fafb;
  --border-color: #374151;
}

body {
  background-color: var(--bg-color);
  color: var(--text-color);
  transition: background-color 0.3s, color 0.3s;
}

/* 移动端适配样式 */
@media (max-width: 640px) {
  header h1 {
    font-size: 1.5rem; /* 在移动端缩小标题字体 */
  }
  
  header {
    padding-left: 1rem;
    padding-right: 1rem;
  }
}
